// Column definitions for the scenario table
export const ScenarioTableColumns = {
  POLICY_YEAR: 'Policy Year',
  END_OF_AGE: 'End of Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_OUTLAY: 'Net Outlay',
  NET_SURRENDER_VALUE: 'Net Surrender Value',
  NET_DEATH_BENEFIT: 'Net Death Benefit'
} as const;

// Column configurations with additional display properties
export const ScenarioTableColumnConfig = [
  { 
    key: 'policyYear',
    header: ScenarioTableColumns.POLICY_YEAR,
    width: 100
  },
  { 
    key: 'endOfAge',
    header: ScenarioTableColumns.END_OF_AGE,
    width: 100
  },
  { 
    key: 'plannedPremium',
    header: ScenarioTableColumns.PLANNED_PREMIUM,
    width: 150,
    isCurrency: true
  },
  { 
    key: 'netOutlay',
    header: ScenarioTableColumns.NET_OUTLAY,
    width: 150,
    isCurrency: true
  },
  { 
    key: 'netSurrenderValue',
    header: ScenarioTableColumns.NET_SURRENDER_VALUE,
    width: 180,
    isCurrency: true
  },
  { 
    key: 'netDeathBenefit',
    header: ScenarioTableColumns.NET_DEATH_BENEFIT,
    width: 180,
    isCurrency: true
  }
];

export interface ScenarioTableData {
  policyYear: number;
  endOfAge: number;
  plannedPremium: number;
  netOutlay: number;
  netSurrenderValue: number;
  netDeathBenefit: number;
}

// ===== CASH VALUE ANALYSIS TABLE =====
export const As_Is_Loan = {
  POLICY_ID: 'Policy ID',
  SCENARIO_ID: 'Scenario ID',
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  COST_OF_INSURANCE: 'Cost of Insurance',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;


export const AsIsLoan = [
  { key: 'policyId', header: As_Is_Loan.POLICY_ID, width: 100 },
  { key: 'scenarioId', header: As_Is_Loan.SCENARIO_ID, width: 120 },
  { key: 'policyYear', header: As_Is_Loan.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: As_Is_Loan.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: As_Is_Loan.AGE, width: 80 },
  { key: 'plannedPremium', header: As_Is_Loan.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: As_Is_Loan.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: As_Is_Loan.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: As_Is_Loan.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: As_Is_Loan.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'costOfInsurance', header: As_Is_Loan.COST_OF_INSURANCE, width: 160, isCurrency: true },
  { key: 'withdrawal', header: As_Is_Loan.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: As_Is_Loan.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: As_Is_Loan.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: As_Is_Loan.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: As_Is_Loan.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: As_Is_Loan.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: As_Is_Loan.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: As_Is_Loan.NET_CASH_VALUE, width: 140, isCurrency: true }
];


export interface AsIsLoanTableData {
  policyId: number;
  scenarioId: number;
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  costOfInsurance: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}


// ===== DEATH BENEFIT ANALYSIS TABLE =====
export const Face_Amount = {
  POLICY_ID: 'Policy ID',
  SCENARIO_ID: 'Scenario ID',
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  COST_OF_INSURANCE: 'Cost of Insurance',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;


export const FaceAmount = [
  { key: 'policyId', header: Face_Amount.POLICY_ID, width: 100 },
  { key: 'scenarioId', header: Face_Amount.SCENARIO_ID, width: 120 },
  { key: 'policyYear', header: Face_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Face_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Face_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Face_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Face_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Face_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Face_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Face_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'costOfInsurance', header: Face_Amount.COST_OF_INSURANCE, width: 160, isCurrency: true },
  { key: 'withdrawal', header: Face_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Face_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Face_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Face_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Face_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Face_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Face_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Face_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];



export interface FaceAmountTableData {
  policyId: number;
  scenarioId: number;
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  costOfInsurance: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

// ===== POLICY PERFORMANCE TABLE =====
export const Face_Amount_Varies_By_Year = {
  POLICY_ID: 'Policy ID',
  SCENARIO_ID: 'Scenario ID',
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  COST_OF_INSURANCE: 'Cost of Insurance',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;


export const FaceAmountVariesByYear = [
  { key: 'policyId', header: Face_Amount_Varies_By_Year.POLICY_ID, width: 100 },
  { key: 'scenarioId', header: Face_Amount_Varies_By_Year.SCENARIO_ID, width: 120 },
  { key: 'policyYear', header: Face_Amount_Varies_By_Year.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Face_Amount_Varies_By_Year.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Face_Amount_Varies_By_Year.AGE, width: 80 },
  { key: 'plannedPremium', header: Face_Amount_Varies_By_Year.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Face_Amount_Varies_By_Year.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Face_Amount_Varies_By_Year.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Face_Amount_Varies_By_Year.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Face_Amount_Varies_By_Year.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'costOfInsurance', header: Face_Amount_Varies_By_Year.COST_OF_INSURANCE, width: 160, isCurrency: true },
  { key: 'withdrawal', header: Face_Amount_Varies_By_Year.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Face_Amount_Varies_By_Year.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Face_Amount_Varies_By_Year.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Face_Amount_Varies_By_Year.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Face_Amount_Varies_By_Year.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Face_Amount_Varies_By_Year.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Face_Amount_Varies_By_Year.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Face_Amount_Varies_By_Year.NET_CASH_VALUE, width: 140, isCurrency: true }
];


export interface FaceAmountVariesByYearTableData {
  policyId: number;
  scenarioId: number;
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  costOfInsurance: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

// ===== RISK ASSESSMENT TABLE =====
export const Premium_Amount = {
  POLICY_ID: 'Policy ID',
  SCENARIO_ID: 'Scenario ID',
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  COST_OF_INSURANCE: 'Cost of Insurance',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const PremiumAmount = [
  { key: 'policyId', header: Premium_Amount.POLICY_ID, width: 100 },
  { key: 'scenarioId', header: Premium_Amount.SCENARIO_ID, width: 120 },
  { key: 'policyYear', header: Premium_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Premium_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Premium_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Premium_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Premium_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Premium_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Premium_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Premium_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'costOfInsurance', header: Premium_Amount.COST_OF_INSURANCE, width: 160, isCurrency: true },
  { key: 'withdrawal', header: Premium_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Premium_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Premium_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Premium_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Premium_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Premium_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Premium_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Premium_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface PremiumAmountTableData {
  policyId: number;
  scenarioId: number;
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  costOfInsurance: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

export const Stop_Premium_Amount = {
  POLICY_ID: 'Policy ID',
  SCENARIO_ID: 'Scenario ID',
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  COST_OF_INSURANCE: 'Cost of Insurance',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const StopPremiumAmount = [
  { key: 'policyId', header: Stop_Premium_Amount.POLICY_ID, width: 100 },
  { key: 'scenarioId', header: Stop_Premium_Amount.SCENARIO_ID, width: 120 },
  { key: 'policyYear', header: Stop_Premium_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Stop_Premium_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Stop_Premium_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Stop_Premium_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Stop_Premium_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Stop_Premium_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Stop_Premium_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Stop_Premium_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'costOfInsurance', header: Stop_Premium_Amount.COST_OF_INSURANCE, width: 160, isCurrency: true },
  { key: 'withdrawal', header: Stop_Premium_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Stop_Premium_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Stop_Premium_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Stop_Premium_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Stop_Premium_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Stop_Premium_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Stop_Premium_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Stop_Premium_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface StopPremiumAmountTableData {
  policyId: number;
  scenarioId: number;
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  costOfInsurance: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}


export const generateMockTableData = (scenario: any): ScenarioTableData[] => {
  console.log('📊 Generating table data for scenario:', scenario.id, scenario.name);

  // Generate mock table data for 10 years (2025-2034)
  const tableData: ScenarioTableData[] = [
    {
      policyYear: 1,
      endOfAge: 40,
      plannedPremium: 10000,
      netOutlay: 5000,
      netSurrenderValue: 50000,
      netDeathBenefit: 250000
    },
    {
      policyYear: 2,
      endOfAge: 41,
      plannedPremium: 10000,
      netOutlay: 5500,
      netSurrenderValue: 51000,
      netDeathBenefit: 255000
    },
    {
      policyYear: 3,
      endOfAge: 42,
      plannedPremium: 10000,
      netOutlay: 6000,
      netSurrenderValue: 52000,
      netDeathBenefit: 260000
    },
    {
      policyYear: 4,
      endOfAge: 43,
      plannedPremium: 10000,
      netOutlay: 6500,
      netSurrenderValue: 53000,
      netDeathBenefit: 265000
    },
    {
      policyYear: 5,
      endOfAge: 44,
      plannedPremium: 10000,
      netOutlay: 7000,
      netSurrenderValue: 54000,
      netDeathBenefit: 270000
    },
    {
      policyYear: 6,
      endOfAge: 45,
      plannedPremium: 10000,
      netOutlay: 7500,
      netSurrenderValue: 55000,
      netDeathBenefit: 275000
    },
    {
      policyYear: 7,
      endOfAge: 46,
      plannedPremium: 10000,
      netOutlay: 8000,
      netSurrenderValue: 56000,
      netDeathBenefit: 280000
    },
    {
      policyYear: 8,
      endOfAge: 47,
      plannedPremium: 10000,
      netOutlay: 8500,
      netSurrenderValue: 57000,
      netDeathBenefit: 285000
    },
    {
      policyYear: 9,
      endOfAge: 48,
      plannedPremium: 10000,
      netOutlay: 9000,
      netSurrenderValue: 58000,
      netDeathBenefit: 290000
    },
    {
      policyYear: 10,
      endOfAge: 49,
      plannedPremium: 10000,
      netOutlay: 9500,
      netSurrenderValue: 59000,
      netDeathBenefit: 295000
    }
  ];

  console.log('✅ Generated table data:', tableData.length, 'rows');
  return tableData;
};

// ===== GENERATE MOCK DATA FOR ALL TABLES =====

export const generateAsIsLoanTableData = () : AsIsLoanTableData[] => {
  return [

  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 6,
    calendarYear: 2025,
    age: 40,
    plannedPremium: 2500,
    netValueBeginningOfYear: 12500,
    interestRate: 0.03,
    interestAmount: 375,
    faceAmount: 350000,
    costOfInsurance: 300,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 525,
    loanRepayment: 600,
    loanOutstanding: 10425,
    charges: 100,
    netCashValue: 12550
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 7,
    calendarYear: 2026,
    age: 41,
    plannedPremium: 2500,
    netValueBeginningOfYear: 15050,
    interestRate: 0.03,
    interestAmount: 451.5,
    faceAmount: 350000,
    costOfInsurance: 306,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 521.25,
    loanRepayment: 600,
    loanOutstanding: 10346.25,
    charges: 100,
    netCashValue: 15174.25
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 8,
    calendarYear: 2027,
    age: 42,
    plannedPremium: 2500,
    netValueBeginningOfYear: 17674.25,
    interestRate: 0.03,
    interestAmount: 530.23,
    faceAmount: 350000,
    costOfInsurance: 312,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 517.31,
    loanRepayment: 600,
    loanOutstanding: 10263.56,
    charges: 100,
    netCashValue: 17875.17
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 9,
    calendarYear: 2028,
    age: 43,
    plannedPremium: 2500,
    netValueBeginningOfYear: 20375.17,
    interestRate: 0.03,
    interestAmount: 611.25,
    faceAmount: 350000,
    costOfInsurance: 318,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 513.18,
    loanRepayment: 600,
    loanOutstanding: 10176.74,
    charges: 100,
    netCashValue: 20655.24
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 10,
    calendarYear: 2029,
    age: 44,
    plannedPremium: 2500,
    netValueBeginningOfYear: 23155.24,
    interestRate: 0.03,
    interestAmount: 694.66,
    faceAmount: 350000,
    costOfInsurance: 324,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 508.84,
    loanRepayment: 600,
    loanOutstanding: 10085.58,
    charges: 100,
    netCashValue: 23517.06
  },
   {
    policyId: 1,
    scenarioId: 1,
    policyYear: 11,
    calendarYear: 2030,
    age: 45,
    plannedPremium: 2500,
    netValueBeginningOfYear: 26017.06,
    interestRate: 0.03,
    interestAmount: 780.51,
    faceAmount: 350000,
    costOfInsurance: 330,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 504.28,
    loanRepayment: 600,
    loanOutstanding: 9989.86,
    charges: 100,
    netCashValue: 26463.30
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 12,
    calendarYear: 2031,
    age: 46,
    plannedPremium: 2500,
    netValueBeginningOfYear: 28963.30,
    interestRate: 0.03,
    interestAmount: 868.90,
    faceAmount: 350000,
    costOfInsurance: 336,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 499.49,
    loanRepayment: 600,
    loanOutstanding: 9889.35,
    charges: 100,
    netCashValue: 29496.70
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 13,
    calendarYear: 2032,
    age: 47,
    plannedPremium: 2500,
    netValueBeginningOfYear: 31996.70,
    interestRate: 0.03,
    interestAmount: 959.90,
    faceAmount: 350000,
    costOfInsurance: 342,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 494.47,
    loanRepayment: 600,
    loanOutstanding: 9783.82,
    charges: 100,
    netCashValue: 32620.13
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 14,
    calendarYear: 2033,
    age: 48,
    plannedPremium: 2500,
    netValueBeginningOfYear: 35120.13,
    interestRate: 0.03,
    interestAmount: 1053.60,
    faceAmount: 350000,
    costOfInsurance: 348,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 489.19,
    loanRepayment: 600,
    loanOutstanding: 9673.01,
    charges: 100,
    netCashValue: 35836.55
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 15,
    calendarYear: 2034,
    age: 49,
    plannedPremium: 2500,
    netValueBeginningOfYear: 38336.55,
    interestRate: 0.03,
    interestAmount: 1150.10,
    faceAmount: 350000,
    costOfInsurance: 354,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 483.65,
    loanRepayment: 600,
    loanOutstanding: 9556.66,
    charges: 100,
    netCashValue: 39148.99
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 16,
    calendarYear: 2035,
    age: 50,
    plannedPremium: 2500,
    netValueBeginningOfYear: 41648.99,
    interestRate: 0.03,
    interestAmount: 1249.47,
    faceAmount: 350000,
    costOfInsurance: 360,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 477.83,
    loanRepayment: 600,
    loanOutstanding: 9434.49,
    charges: 100,
    netCashValue: 42560.63
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 17,
    calendarYear: 2036,
    age: 51,
    plannedPremium: 2500,
    netValueBeginningOfYear: 45060.63,
    interestRate: 0.03,
    interestAmount: 1351.82,
    faceAmount: 350000,
    costOfInsurance: 366,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 471.72,
    loanRepayment: 600,
    loanOutstanding: 9306.22,
    charges: 100,
    netCashValue: 46074.73
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 18,
    calendarYear: 2037,
    age: 52,
    plannedPremium: 2500,
    netValueBeginningOfYear: 48574.73,
    interestRate: 0.03,
    interestAmount: 1457.24,
    faceAmount: 350000,
    costOfInsurance: 372,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 465.31,
    loanRepayment: 600,
    loanOutstanding: 9171.53,
    charges: 100,
    netCashValue: 49694.66
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 19,
    calendarYear: 2038,
    age: 53,
    plannedPremium: 2500,
    netValueBeginningOfYear: 52194.66,
    interestRate: 0.03,
    interestAmount: 1565.84,
    faceAmount: 350000,
    costOfInsurance: 378,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 458.58,
    loanRepayment: 600,
    loanOutstanding: 9030.10,
    charges: 100,
    netCashValue: 53423.92
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 20,
    calendarYear: 2039,
    age: 54,
    plannedPremium: 2500,
    netValueBeginningOfYear: 55923.92,
    interestRate: 0.03,
    interestAmount: 1677.72,
    faceAmount: 350000,
    costOfInsurance: 384,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 451.51,
    loanRepayment: 600,
    loanOutstanding: 8881.61,
    charges: 100,
    netCashValue: 57266.13
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 21,
    calendarYear: 2040,
    age: 55,
    plannedPremium: 2500,
    netValueBeginningOfYear: 59766.13,
    interestRate: 0.03,
    interestAmount: 1792.98,
    faceAmount: 350000,
    costOfInsurance: 390,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 444.08,
    loanRepayment: 600,
    loanOutstanding: 8725.69,
    charges: 100,
    netCashValue: 61225.04
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 22,
    calendarYear: 2041,
    age: 56,
    plannedPremium: 2500,
    netValueBeginningOfYear: 63725.04,
    interestRate: 0.03,
    interestAmount: 1911.75,
    faceAmount: 350000,
    costOfInsurance: 396,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 436.28,
    loanRepayment: 600,
    loanOutstanding: 8561.97,
    charges: 100,
    netCashValue: 65304.50
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 23,
    calendarYear: 2042,
    age: 57,
    plannedPremium: 2500,
    netValueBeginningOfYear: 67804.50,
    interestRate: 0.03,
    interestAmount: 2034.14,
    faceAmount: 350000,
    costOfInsurance: 402,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 428.10,
    loanRepayment: 600,
    loanOutstanding: 8390.07,
    charges: 100,
    netCashValue: 69508.54
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 24,
    calendarYear: 2043,
    age: 58,
    plannedPremium: 2500,
    netValueBeginningOfYear: 72008.54,
    interestRate: 0.03,
    interestAmount: 2160.26,
    faceAmount: 350000,
    costOfInsurance: 408,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 419.50,
    loanRepayment: 600,
    loanOutstanding: 8209.57,
    charges: 100,
    netCashValue: 73841.29
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 25,
    calendarYear: 2044,
    age: 59,
    plannedPremium: 2500,
    netValueBeginningOfYear: 76341.29,
    interestRate: 0.03,
    interestAmount: 2290.24,
    faceAmount: 350000,
    costOfInsurance: 414,
    withdrawal: 0,
    policyLoan: 0,
    loanInterestRate: 0.05,
    loanInterest: 410.48,
    loanRepayment: 600,
    loanOutstanding: 8020.05,
    charges: 100,
    netCashValue: 78307.05
  },
   {
    policyId: 1,
    scenarioId: 1,
    policyYear: 26,
    calendarYear: 2045,
    age: 60,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 80807.05,
    interestRate: 0.03,
    interestAmount: 2424.21,
    faceAmount: 350000.00,
    costOfInsurance: 420.00,
    withdrawal: 0,
    policyLoan:0,
    loanInterestRate: 0.05,
    loanInterest: 401.00,
    loanRepayment: 600.00,
    loanOutstanding: 7821.06,
    charges: 100.00,
    netCashValue: 82910.26
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 27,
    calendarYear: 2046,
    age: 61,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 85410.26,
    interestRate: 0.03,
    interestAmount: 2562.31,
    faceAmount: 350000.00,
    costOfInsurance: 426.00,
    withdrawal: 0,
    policyLoan:0,
    loanInterestRate: 0.05,
    loanInterest: 391.05,
    loanRepayment: 600.00,
    loanOutstanding: 7612.11,
    charges: 100.00,
    netCashValue: 87655.52
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 28,
    calendarYear: 2047,
    age: 62,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 90155.52,
    interestRate: 0.03,
    interestAmount: 2704.67,
    faceAmount: 350000.00,
    costOfInsurance: 432.00,
    withdrawal: 0,
    policyLoan:0,
    loanInterestRate: 0.05,
    loanInterest: 380.61,
    loanRepayment: 600.00,
    loanOutstanding: 7392.71,
    charges: 100.00,
    netCashValue: 92547.58
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 29,
    calendarYear: 2048,
    age: 63,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 95047.58,
    interestRate: 0.03,
    interestAmount: 2851.43,
    faceAmount: 350000.00,
    costOfInsurance: 438.00,
    withdrawal: 0,
    policyLoan:0,
    loanInterestRate: 0.05,
    loanInterest: 369.64,
    loanRepayment: 600.00,
    loanOutstanding: 7162.35,
    charges: 100.00,
    netCashValue: 97591.37
  },
  {
    policyId: 1,
    scenarioId: 1,
    policyYear: 30,
    calendarYear: 2049,
    age: 64,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 100091.37,
    interestRate: 0.03,
    interestAmount: 3002.74,
    faceAmount: 350000.00,
    costOfInsurance: 444.00,
    withdrawal: 0,
    policyLoan:0,
    loanInterestRate: 0.05,
    loanInterest: 358.12,
    loanRepayment: 600.00,
    loanOutstanding: 6920.47,
    charges: 100.00,
    netCashValue: 102791.99
  }
    // Add more rows here as needed...
  ];
};
export const generateFaceAmountTableData = (): FaceAmountTableData[] => {
  return [
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 6,
    calendarYear: 2025,
    age: 40,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 12500.00,
    interestRate: 0.03,
    interestAmount: 375.00,
    faceAmount: 400000.00,
    costOfInsurance: 342.86,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 525.00,
    loanRepayment: 600.00,
    loanOutstanding: 10425.00,
    charges: 100.00,
    netCashValue: 12507.14
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 7,
    calendarYear: 2026,
    age: 41,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 15007.14,
    interestRate: 0.03,
    interestAmount: 450.21,
    faceAmount: 400000.00,
    costOfInsurance: 349.71,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 521.25,
    loanRepayment: 600.00,
    loanOutstanding: 10346.25,
    charges: 100.00,
    netCashValue: 15086.39
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 8,
    calendarYear: 2027,
    age: 42,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 17586.39,
    interestRate: 0.03,
    interestAmount: 527.59,
    faceAmount: 400000.00,
    costOfInsurance: 356.57,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 517.31,
    loanRepayment: 600.00,
    loanOutstanding: 10263.56,
    charges: 100.00,
    netCashValue: 17740.10
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 9,
    calendarYear: 2028,
    age: 43,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 20240.10,
    interestRate: 0.03,
    interestAmount: 607.20,
    faceAmount: 400000.00,
    costOfInsurance: 363.43,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 513.18,
    loanRepayment: 600.00,
    loanOutstanding: 10176.74,
    charges: 100.00,
    netCashValue: 20470.70
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 10,
    calendarYear: 2029,
    age: 44,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 22970.70,
    interestRate: 0.03,
    interestAmount: 689.12,
    faceAmount: 400000.00,
    costOfInsurance: 370.29,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 508.84,
    loanRepayment: 600.00,
    loanOutstanding: 10085.58,
    charges: 100.00,
    netCashValue: 23280.70
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 11,
    calendarYear: 2030,
    age: 45,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 25780.70,
    interestRate: 0.03,
    interestAmount: 773.42,
    faceAmount: 400000.00,
    costOfInsurance: 377.14,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 504.28,
    loanRepayment: 600.00,
    loanOutstanding: 9989.86,
    charges: 100.00,
    netCashValue: 26172.69
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 12,
    calendarYear: 2031,
    age: 46,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 28672.69,
    interestRate: 0.03,
    interestAmount: 860.18,
    faceAmount: 400000.00,
    costOfInsurance: 384.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 499.49,
    loanRepayment: 600.00,
    loanOutstanding: 9889.35,
    charges: 100.00,
    netCashValue: 29149.38
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 13,
    calendarYear: 2032,
    age: 47,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 31649.38,
    interestRate: 0.03,
    interestAmount: 949.48,
    faceAmount: 400000.00,
    costOfInsurance: 390.86,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 494.47,
    loanRepayment: 600.00,
    loanOutstanding: 9783.82,
    charges: 100.00,
    netCashValue: 32213.54
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 14,
    calendarYear: 2033,
    age: 48,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 34713.54,
    interestRate: 0.03,
    interestAmount: 1041.41,
    faceAmount: 400000.00,
    costOfInsurance: 397.71,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 489.19,
    loanRepayment: 600.00,
    loanOutstanding: 9673.01,
    charges: 100.00,
    netCashValue: 35368.04
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 15,
    calendarYear: 2034,
    age: 49,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 37868.04,
    interestRate: 0.03,
    interestAmount: 1136.04,
    faceAmount: 400000.00,
    costOfInsurance: 404.57,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 483.65,
    loanRepayment: 600.00,
    loanOutstanding: 9556.66,
    charges: 100.00,
    netCashValue: 38615.86
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 16,
    calendarYear: 2035,
    age: 50,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 41115.86,
    interestRate: 0.03,
    interestAmount: 1233.48,
    faceAmount: 400000.00,
    costOfInsurance: 411.43,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 477.83,
    loanRepayment: 600.00,
    loanOutstanding: 9434.49,
    charges: 100.00,
    netCashValue: 41960.07
  },
  {
    policyId: 1,
    scenarioId: 2,
    policyYear: 17,
    calendarYear: 2036,
    age: 51,
    plannedPremium: 2500.00,
    netValueBeginningOfYear: 44460.07,
    interestRate: 0.03,
    interestAmount: 1333.80,
    faceAmount: 400000.00,
    costOfInsurance: 418.29,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 471.72,
    loanRepayment: 600.00,
    loanOutstanding: 9306.22,
    charges: 100.00,
    netCashValue: 45403.87
  },
  {
  policyId: 1,
  scenarioId: 2,
  policyYear: 18,
  calendarYear: 2037,
  age: 52,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 47903.87,
  interestRate: 0.03,
  interestAmount: 1437.12,
  faceAmount: 400000.00,
  costOfInsurance: 425.14,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 465.31,
  loanRepayment: 600.00,
  loanOutstanding: 9171.53,
  charges: 100.00,
  netCashValue: 48950.53
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 19,
  calendarYear: 2038,
  age: 53,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 51450.53,
  interestRate: 0.03,
  interestAmount: 1543.52,
  faceAmount: 400000.00,
  costOfInsurance: 432.00,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 458.58,
  loanRepayment: 600.00,
  loanOutstanding: 9030.10,
  charges: 100.00,
  netCashValue: 52603.47
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 20,
  calendarYear: 2039,
  age: 54,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 55103.47,
  interestRate: 0.03,
  interestAmount: 1653.10,
  faceAmount: 400000.00,
  costOfInsurance: 438.86,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 451.51,
  loanRepayment: 600.00,
  loanOutstanding: 8881.61,
  charges: 100.00,
  netCashValue: 56366.21
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 21,
  calendarYear: 2040,
  age: 55,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 58866.21,
  interestRate: 0.03,
  interestAmount: 1765.99,
  faceAmount: 400000.00,
  costOfInsurance: 445.71,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 444.08,
  loanRepayment: 600.00,
  loanOutstanding: 8725.69,
  charges: 100.00,
  netCashValue: 60242.40
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 22,
  calendarYear: 2041,
  age: 56,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 62742.40,
  interestRate: 0.03,
  interestAmount: 1882.27,
  faceAmount: 400000.00,
  costOfInsurance: 452.57,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 436.28,
  loanRepayment: 600.00,
  loanOutstanding: 8561.97,
  charges: 100.00,
  netCashValue: 64235.82
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 23,
  calendarYear: 2042,
  age: 57,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 66735.82,
  interestRate: 0.03,
  interestAmount: 2002.07,
  faceAmount: 400000.00,
  costOfInsurance: 459.43,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 428.10,
  loanRepayment: 600.00,
  loanOutstanding: 8390.07,
  charges: 100.00,
  netCashValue: 68350.36
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 24,
  calendarYear: 2043,
  age: 58,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 70850.36,
  interestRate: 0.03,
  interestAmount: 2125.51,
  faceAmount: 400000.00,
  costOfInsurance: 466.29,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 419.50,
  loanRepayment: 600.00,
  loanOutstanding: 8209.57,
  charges: 100.00,
  netCashValue: 72590.09
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 25,
  calendarYear: 2044,
  age: 59,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 75090.09,
  interestRate: 0.03,
  interestAmount: 2252.70,
  faceAmount: 400000.00,
  costOfInsurance: 473.14,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 410.48,
  loanRepayment: 600.00,
  loanOutstanding: 8020.05,
  charges: 100.00,
  netCashValue: 76959.17
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 26,
  calendarYear: 2045,
  age: 60,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 79459.17,
  interestRate: 0.03,
  interestAmount: 2383.78,
  faceAmount: 400000.00,
  costOfInsurance: 480.00,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 401.00,
  loanRepayment: 600.00,
  loanOutstanding: 7821.06,
  charges: 100.00,
  netCashValue: 81461.94
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 27,
  calendarYear: 2046,
  age: 61,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 83961.94,
  interestRate: 0.03,
  interestAmount: 2518.86,
  faceAmount: 400000.00,
  costOfInsurance: 486.86,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 391.05,
  loanRepayment: 600.00,
  loanOutstanding: 7612.11,
  charges: 100.00,
  netCashValue: 86102.89
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 28,
  calendarYear: 2047,
  age: 62,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 88602.89,
  interestRate: 0.03,
  interestAmount: 2658.09,
  faceAmount: 400000.00,
  costOfInsurance: 493.71,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 380.61,
  loanRepayment: 600.00,
  loanOutstanding: 7392.71,
  charges: 100.00,
  netCashValue: 90886.65
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 29,
  calendarYear: 2048,
  age: 63,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 93386.65,
  interestRate: 0.03,
  interestAmount: 2801.60,
  faceAmount: 400000.00,
  costOfInsurance: 500.57,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 369.64,
  loanRepayment: 600.00,
  loanOutstanding: 7162.35,
  charges: 100.00,
  netCashValue: 95818.05
},
{
  policyId: 1,
  scenarioId: 2,
  policyYear: 30,
  calendarYear: 2049,
  age: 64,
  plannedPremium: 2500.00,
  netValueBeginningOfYear: 98318.05,
  interestRate: 0.03,
  interestAmount: 2949.54,
  faceAmount: 400000.00,
  costOfInsurance: 507.43,
  withdrawal: 0.00,
  policyLoan: 0.00,
  loanInterestRate: 0.05,
  loanInterest: 358.12,
  loanRepayment: 600.00,
  loanOutstanding: 6920.47,
  charges: 100.00,
  netCashValue: 100902.04
}
];

};

export const generateFaceAmountVariesByYearTableData = (): FaceAmountVariesByYearTableData[] => {
  return [
    {
    policyId: 1,
    scenarioId: 3,
    policyYear: 6,
    calendarYear: 2025,
    age: 35,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 12500.0,
    interestRate: 0.03,
    interestAmount: 375.0,
    faceAmount: 200000.0,
    costOfInsurance: 270.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 525.0,
    loanRepayment: 600.0,
    loanOutstanding: 10425.0,
    charges: 100.0,
    netCashValue: 12580.0
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 7,
    calendarYear: 2026,
    age: 36,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 15080.0,
    interestRate: 0.03,
    interestAmount: 452.4,
    faceAmount: 200000.0,
    costOfInsurance: 276.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 521.25,
    loanRepayment: 600.0,
    loanOutstanding: 10346.25,
    charges: 100.0,
    netCashValue: 15235.15
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 8,
    calendarYear: 2027,
    age: 37,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 17735.15,
    interestRate: 0.03,
    interestAmount: 532.05,
    faceAmount: 250000.0,
    costOfInsurance: 282.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 517.31,
    loanRepayment: 600.0,
    loanOutstanding: 10263.56,
    charges: 100.0,
    netCashValue: 17967.89
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 9,
    calendarYear: 2028,
    age: 38,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 20467.89,
    interestRate: 0.03,
    interestAmount: 614.04,
    faceAmount: 300000.0,
    costOfInsurance: 288.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 513.18,
    loanRepayment: 600.0,
    loanOutstanding: 10176.74,
    charges: 100.0,
    netCashValue: 20780.75
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 10,
    calendarYear: 2029,
    age: 39,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 23280.75,
    interestRate: 0.03,
    interestAmount: 698.42,
    faceAmount: 300000.0,
    costOfInsurance: 882.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 508.84,
    loanRepayment: 600.0,
    loanOutstanding: 10085.58,
    charges: 100.0,
    netCashValue: 23088.35
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 11,
    calendarYear: 2030,
    age: 40,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 25588.34,
    interestRate: 0.03,
    interestAmount: 767.29,
    faceAmount: 300000.0,
    costOfInsurance: 300.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 504.28,
    loanRepayment: 600.0,
    loanOutstanding: 9989.86,
    charges: 100.0,
    netCashValue: 25451.71
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 12,
    calendarYear: 2031,
    age: 41,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 27951.71,
    interestRate: 0.03,
    interestAmount: 838.55,
    faceAmount: 300000.0,
    costOfInsurance: 306.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 499.49,
    loanRepayment: 600.0,
    loanOutstanding: 9889.35,
    charges: 100.0,
    netCashValue: 27872.77
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 13,
    calendarYear: 2032,
    age: 42,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 30372.77,
    interestRate: 0.03,
    interestAmount: 911.18,
    faceAmount: 300000.0,
    costOfInsurance: 312.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 494.47,
    loanRepayment: 600.0,
    loanOutstanding: 9783.82,
    charges: 100.0,
    netCashValue: 30353.48
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 14,
    calendarYear: 2033,
    age: 43,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 32853.48,
    interestRate: 0.03,
    interestAmount: 985.6,
    faceAmount: 300000.0,
    costOfInsurance: 318.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 489.19,
    loanRepayment: 600.0,
    loanOutstanding: 9673.01,
    charges: 100.0,
    netCashValue: 32895.89
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 15,
    calendarYear: 2034,
    age: 44,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 35395.89,
    interestRate: 0.03,
    interestAmount: 1061.88,
    faceAmount: 300000.0,
    costOfInsurance: 324.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 483.65,
    loanRepayment: 600.0,
    loanOutstanding: 9556.66,
    charges: 100.0,
    netCashValue: 35502.12
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 16,
    calendarYear: 2035,
    age: 45,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 38002.12,
    interestRate: 0.03,
    interestAmount: 1140.06,
    faceAmount: 300000.0,
    costOfInsurance: 330.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 477.83,
    loanRepayment: 600.0,
    loanOutstanding: 9434.49,
    charges: 100.0,
    netCashValue: 38174.35
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 17,
    calendarYear: 2036,
    age: 46,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 40674.35,
    interestRate: 0.03,
    interestAmount: 1220.23,
    faceAmount: 300000.0,
    costOfInsurance: 336.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 471.72,
    loanRepayment: 600.0,
    loanOutstanding: 9306.22,
    charges: 100.0,
    netCashValue: 40914.86
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 18,
    calendarYear: 2037,
    age: 47,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 43414.86,
    interestRate: 0.03,
    interestAmount: 1302.45,
    faceAmount: 300000.0,
    costOfInsurance: 342.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 465.31,
    loanRepayment: 600.0,
    loanOutstanding: 9171.53,
    charges: 100.0,
    netCashValue: 43725.99
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 19,
    calendarYear: 2038,
    age: 48,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 46225.99,
    interestRate: 0.03,
    interestAmount: 1386.78,
    faceAmount: 300000.0,
    costOfInsurance: 348.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 458.58,
    loanRepayment: 600.0,
    loanOutstanding: 9030.1,
    charges: 100.0,
    netCashValue: 46610.2
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 20,
    calendarYear: 2039,
    age: 49,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 49110.2,
    interestRate: 0.03,
    interestAmount: 1473.31,
    faceAmount: 300000.0,
    costOfInsurance: 354.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 451.51,
    loanRepayment: 600.0,
    loanOutstanding: 8881.61,
    charges: 100.0,
    netCashValue: 49570.0
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 21,
    calendarYear: 2040,
    age: 50,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 52070.0,
    interestRate: 0.03,
    interestAmount: 1562.1,
    faceAmount: 300000.0,
    costOfInsurance: 1080.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 444.08,
    loanRepayment: 600.0,
    loanOutstanding: 8725.69,
    charges: 100.0,
    netCashValue: 52608.02
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 22,
    calendarYear: 2041,
    age: 51,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 55108.02,
    interestRate: 0.03,
    interestAmount: 1653.24,
    faceAmount: 300000.0,
    costOfInsurance: 1098.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 436.28,
    loanRepayment: 600.0,
    loanOutstanding: 8561.97,
    charges: 100.0,
    netCashValue: 55726.97
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 23,
    calendarYear: 2042,
    age: 52,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 58226.97,
    interestRate: 0.03,
    interestAmount: 1746.81,
    faceAmount: 300000.0,
    costOfInsurance: 1116.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 428.1,
    loanRepayment: 600.0,
    loanOutstanding: 8390.07,
    charges: 100.0,
    netCashValue: 58929.68
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 24,
    calendarYear: 2043,
    age: 53,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 61429.68,
    interestRate: 0.03,
    interestAmount: 1842.89,
    faceAmount: 300000.0,
    costOfInsurance: 1134.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 419.5,
    loanRepayment: 600.0,
    loanOutstanding: 8209.57,
    charges: 100.0,
    netCashValue: 62219.07
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 25,
    calendarYear: 2044,
    age: 54,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 64719.07,
    interestRate: 0.03,
    interestAmount: 1941.57,
    faceAmount: 300000.0,
    costOfInsurance: 1152.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 410.48,
    loanRepayment: 600.0,
    loanOutstanding: 8020.05,
    charges: 100.0,
    netCashValue: 65598.16
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 26,
    calendarYear: 2045,
    age: 55,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 68098.16,
    interestRate: 0.03,
    interestAmount: 2042.94,
    faceAmount: 300000.0,
    costOfInsurance: 1170.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 401.0,
    loanRepayment: 600.0,
    loanOutstanding: 7821.06,
    charges: 100.0,
    netCashValue: 69070.11
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 27,
    calendarYear: 2046,
    age: 56,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 71570.11,
    interestRate: 0.03,
    interestAmount: 2147.1,
    faceAmount: 300000.0,
    costOfInsurance: 1188.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 391.05,
    loanRepayment: 600.0,
    loanOutstanding: 7612.11,
    charges: 100.0,
    netCashValue: 72638.16
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 28,
    calendarYear: 2047,
    age: 57,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 75138.16,
    interestRate: 0.03,
    interestAmount: 2254.14,
    faceAmount: 300000.0,
    costOfInsurance: 1206.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 380.61,
    loanRepayment: 600.0,
    loanOutstanding: 7392.71,
    charges: 100.0,
    netCashValue: 76305.7
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 29,
    calendarYear: 2048,
    age: 58,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 78805.7,
    interestRate: 0.03,
    interestAmount: 2364.17,
    faceAmount: 300000.0,
    costOfInsurance: 1224.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 369.64,
    loanRepayment: 600.0,
    loanOutstanding: 7162.35,
    charges: 100.0,
    netCashValue: 80076.23
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 30,
    calendarYear: 2049,
    age: 59,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 82576.23,
    interestRate: 0.03,
    interestAmount: 2477.29,
    faceAmount: 300000.0,
    costOfInsurance: 1242.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 358.12,
    loanRepayment: 600.0,
    loanOutstanding: 6920.47,
    charges: 100.0,
    netCashValue: 83953.4
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 31,
    calendarYear: 2050,
    age: 60,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 86453.4,
    interestRate: 0.03,
    interestAmount: 2593.6,
    faceAmount: 300000.0,
    costOfInsurance: 1260.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 346.02,
    loanRepayment: 600.0,
    loanOutstanding: 6666.49,
    charges: 100.0,
    netCashValue: 87940.98
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 32,
    calendarYear: 2051,
    age: 61,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 90440.98,
    interestRate: 0.03,
    interestAmount: 2713.23,
    faceAmount: 300000.0,
    costOfInsurance: 1278.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 333.32,
    loanRepayment: 600.0,
    loanOutstanding: 6399.82,
    charges: 100.0,
    netCashValue: 92042.88
  },
  {
    policyId: 1,
    scenarioId: 3,
    policyYear: 33,
    calendarYear: 2052,
    age: 62,
    plannedPremium: 2500.0,
    netValueBeginningOfYear: 94542.88,
    interestRate: 0.03,
    interestAmount: 2836.29,
    faceAmount: 300000.0,
    costOfInsurance: 1296.0,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 319.99,
    loanRepayment: 600.0,
    loanOutstanding: 6119.81,
    charges: 100.0,
    netCashValue: 96263.18
  }
  ];
};

export const generatePremiumAmountTableData = (): PremiumAmountTableData[] => {
  return [
    {
    policyId: 1,
    scenarioId: 4,
    policyYear: 6,
    calendarYear: 2025,
    age: 40,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 12500.00,
    interestRate: 0.03,
    interestAmount: 375.00,
    faceAmount: 350000.00,
    costOfInsurance: 300.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 525.00,
    loanRepayment: 600.00,
    loanOutstanding: 10425.00,
    charges: 100.00,
    netCashValue: 12550.00
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 7,
    calendarYear: 2026,
    age: 41,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 15550.00,
    interestRate: 0.03,
    interestAmount: 466.50,
    faceAmount: 350000.00,
    costOfInsurance: 306.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 521.25,
    loanRepayment: 600.00,
    loanOutstanding: 10346.25,
    charges: 100.00,
    netCashValue: 15689.25
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 8,
    calendarYear: 2027,
    age: 42,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 18689.25,
    interestRate: 0.03,
    interestAmount: 560.68,
    faceAmount: 350000.00,
    costOfInsurance: 312.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 517.31,
    loanRepayment: 600.00,
    loanOutstanding: 10263.56,
    charges: 100.00,
    netCashValue: 18920.62
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 9,
    calendarYear: 2028,
    age: 43,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 21920.62,
    interestRate: 0.03,
    interestAmount: 657.62,
    faceAmount: 350000.00,
    costOfInsurance: 318.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 513.18,
    loanRepayment: 600.00,
    loanOutstanding: 10176.74,
    charges: 100.00,
    netCashValue: 22247.06
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 10,
    calendarYear: 2029,
    age: 44,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 25247.06,
    interestRate: 0.03,
    interestAmount: 757.41,
    faceAmount: 350000.00,
    costOfInsurance: 324.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 508.84,
    loanRepayment: 600.00,
    loanOutstanding: 10085.58,
    charges: 100.00,
    netCashValue: 25671.63
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 11,
    calendarYear: 2030,
    age: 45,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 28671.63,
    interestRate: 0.03,
    interestAmount: 860.15,
    faceAmount: 350000.00,
    costOfInsurance: 330.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 504.28,
    loanRepayment: 600.00,
    loanOutstanding: 9989.86,
    charges: 100.00,
    netCashValue: 29197.50
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 12,
    calendarYear: 2031,
    age: 46,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 32197.50,
    interestRate: 0.03,
    interestAmount: 965.92,
    faceAmount: 350000.00,
    costOfInsurance: 336.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 499.49,
    loanRepayment: 600.00,
    loanOutstanding: 9889.35,
    charges: 100.00,
    netCashValue: 32827.93
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 13,
    calendarYear: 2032,
    age: 47,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 35827.93,
    interestRate: 0.03,
    interestAmount: 1074.84,
    faceAmount: 350000.00,
    costOfInsurance: 342.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 494.47,
    loanRepayment: 600.00,
    loanOutstanding: 9783.82,
    charges: 100.00,
    netCashValue: 36566.30
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 14,
    calendarYear: 2033,
    age: 48,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 39566.30,
    interestRate: 0.03,
    interestAmount: 1186.99,
    faceAmount: 350000.00,
    costOfInsurance: 348.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 489.19,
    loanRepayment: 600.00,
    loanOutstanding: 9673.01,
    charges: 100.00,
    netCashValue: 40416.10
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 15,
    calendarYear: 2034,
    age: 49,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 43416.10,
    interestRate: 0.03,
    interestAmount: 1302.48,
    faceAmount: 350000.00,
    costOfInsurance: 354.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 483.65,
    loanRepayment: 600.00,
    loanOutstanding: 9556.66,
    charges: 100.00,
    netCashValue: 44380.93
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 16,
    calendarYear: 2035,
    age: 50,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 47380.93,
    interestRate: 0.03,
    interestAmount: 1421.43,
    faceAmount: 350000.00,
    costOfInsurance: 360.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 477.83,
    loanRepayment: 600.00,
    loanOutstanding: 9434.49,
    charges: 100.00,
    netCashValue: 48464.53
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 17,
    calendarYear: 2036,
    age: 51,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 51464.53,
    interestRate: 0.03,
    interestAmount: 1543.94,
    faceAmount: 350000.00,
    costOfInsurance: 366.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 471.72,
    loanRepayment: 600.00,
    loanOutstanding: 9306.22,
    charges: 100.00,
    netCashValue: 52670.74
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 18,
    calendarYear: 2037,
    age: 52,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 55670.74,
    interestRate: 0.03,
    interestAmount: 1670.12,
    faceAmount: 350000.00,
    costOfInsurance: 372.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 465.31,
    loanRepayment: 600.00,
    loanOutstanding: 9171.53,
    charges: 100.00,
    netCashValue: 57003.55
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 19,
    calendarYear: 2038,
    age: 53,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 60003.55,
    interestRate: 0.03,
    interestAmount: 1800.11,
    faceAmount: 350000.00,
    costOfInsurance: 378.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 458.58,
    loanRepayment: 600.00,
    loanOutstanding: 9030.10,
    charges: 100.00,
    netCashValue: 61467.08
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 20,
    calendarYear: 2039,
    age: 54,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 64467.08,
    interestRate: 0.03,
    interestAmount: 1934.01,
    faceAmount: 350000.00,
    costOfInsurance: 384.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 451.51,
    loanRepayment: 600.00,
    loanOutstanding: 8881.61,
    charges: 100.00,
    netCashValue: 66065.59
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 21,
    calendarYear: 2040,
    age: 55,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 69065.59,
    interestRate: 0.03,
    interestAmount: 2071.97,
    faceAmount: 350000.00,
    costOfInsurance: 390.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 444.08,
    loanRepayment: 600.00,
    loanOutstanding: 8725.69,
    charges: 100.00,
    netCashValue: 70803.48
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 22,
    calendarYear: 2041,
    age: 56,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 73803.48,
    interestRate: 0.03,
    interestAmount: 2214.10,
    faceAmount: 350000.00,
    costOfInsurance: 396.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 436.28,
    loanRepayment: 600.00,
    loanOutstanding: 8561.97,
    charges: 100.00,
    netCashValue: 75685.30
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 23,
    calendarYear: 2042,
    age: 57,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 78685.30,
    interestRate: 0.03,
    interestAmount: 2360.56,
    faceAmount: 350000.00,
    costOfInsurance: 402.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 428.10,
    loanRepayment: 600.00,
    loanOutstanding: 8390.07,
    charges: 100.00,
    netCashValue: 80715.76
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 24,
    calendarYear: 2043,
    age: 58,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 83715.76,
    interestRate: 0.03,
    interestAmount: 2511.47,
    faceAmount: 350000.00,
    costOfInsurance: 408.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 419.50,
    loanRepayment: 600.00,
    loanOutstanding: 8209.57,
    charges: 100.00,
    netCashValue: 85899.73
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 25,
    calendarYear: 2044,
    age: 59,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 88899.73,
    interestRate: 0.03,
    interestAmount: 2666.99,
    faceAmount: 350000.00,
    costOfInsurance: 414.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 410.48,
    loanRepayment: 600.00,
    loanOutstanding: 8020.05,
    charges: 100.00,
    netCashValue: 91242.24
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 26,
    calendarYear: 2045,
    age: 60,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 94242.24,
    interestRate: 0.03,
    interestAmount: 2827.27,
    faceAmount: 350000.00,
    costOfInsurance: 420.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 401.00,
    loanRepayment: 600.00,
    loanOutstanding: 7821.06,
    charges: 100.00,
    netCashValue: 96748.50
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 27,
    calendarYear: 2046,
    age: 61,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 99748.50,
    interestRate: 0.03,
    interestAmount: 2992.46,
    faceAmount: 350000.00,
    costOfInsurance: 426.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 391.05,
    loanRepayment: 600.00,
    loanOutstanding: 7612.11,
    charges: 100.00,
    netCashValue: 102423.91
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 28,
    calendarYear: 2047,
    age: 62,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 105423.91,
    interestRate: 0.03,
    interestAmount: 3162.72,
    faceAmount: 350000.00,
    costOfInsurance: 432.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 380.61,
    loanRepayment: 600.00,
    loanOutstanding: 7392.71,
    charges: 100.00,
    netCashValue: 108274.02
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 29,
    calendarYear: 2048,
    age: 63,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 111274.02,
    interestRate: 0.03,
    interestAmount: 3338.22,
    faceAmount: 350000.00,
    costOfInsurance: 438.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 369.64,
    loanRepayment: 600.00,
    loanOutstanding: 7162.35,
    charges: 100.00,
    netCashValue: 114304.60
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 30,
    calendarYear: 2049,
    age: 64,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 117304.60,
    interestRate: 0.03,
    interestAmount: 3519.14,
    faceAmount: 350000.00,
    costOfInsurance: 444.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 358.12,
    loanRepayment: 600.00,
    loanOutstanding: 6920.47,
    charges: 100.00,
    netCashValue: 120521.62
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 31,
    calendarYear: 2050,
    age: 65,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 123521.62,
    interestRate: 0.03,
    interestAmount: 3705.65,
    faceAmount: 350000.00,
    costOfInsurance: 450.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 346.02,
    loanRepayment: 600.00,
    loanOutstanding: 6666.49,
    charges: 100.00,
    netCashValue: 126931.25
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 32,
    calendarYear: 2051,
    age: 66,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 129931.25,
    interestRate: 0.03,
    interestAmount: 3897.94,
    faceAmount: 350000.00,
    costOfInsurance: 456.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 333.32,
    loanRepayment: 600.00,
    loanOutstanding: 6399.82,
    charges: 100.00,
    netCashValue: 133539.86
  },
  {
    policyId: 1,
    scenarioId: 4,
    policyYear: 33,
    calendarYear: 2052,
    age: 67,
    plannedPremium: 3000.00,
    netValueBeginningOfYear: 136539.86,
    interestRate: 0.03,
    interestAmount: 4096.20,
    faceAmount: 350000.00,
    costOfInsurance: 462.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 319.99,
    loanRepayment: 600.00,
    loanOutstanding: 6119.81,
    charges: 100.00,
    netCashValue: 140354.07
  }
  ];
};

export const generateStopPremiumAmountTableData = (): StopPremiumAmountTableData[] => {
  return [
    {
    policyId: 1,
    scenarioId: 5,
    policyYear: 6,
    calendarYear: 2025,
    age: 40,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 12500.00,
    interestRate: 0.03,
    interestAmount: 375.00,
    faceAmount: 350000.00,
    costOfInsurance: 300.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 12475.00
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 7,
    calendarYear: 2026,
    age: 41,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 12475.00,
    interestRate: 0.03,
    interestAmount: 374.25,
    faceAmount: 350000.00,
    costOfInsurance: 306.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 12443.25
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 8,
    calendarYear: 2027,
    age: 42,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 12443.25,
    interestRate: 0.03,
    interestAmount: 373.30,
    faceAmount: 350000.00,
    costOfInsurance: 312.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 12404.55
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 9,
    calendarYear: 2028,
    age: 43,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 12404.55,
    interestRate: 0.03,
    interestAmount: 372.14,
    faceAmount: 350000.00,
    costOfInsurance: 318.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 12358.68
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 10,
    calendarYear: 2029,
    age: 44,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 12358.68,
    interestRate: 0.03,
    interestAmount: 370.76,
    faceAmount: 350000.00,
    costOfInsurance: 324.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 12305.44
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 11,
    calendarYear: 2030,
    age: 45,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 12305.44,
    interestRate: 0.03,
    interestAmount: 369.16,
    faceAmount: 350000.00,
    costOfInsurance: 330.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 12244.61
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 12,
    calendarYear: 2031,
    age: 46,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 12244.61,
    interestRate: 0.03,
    interestAmount: 367.34,
    faceAmount: 350000.00,
    costOfInsurance: 336.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 12175.95
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 13,
    calendarYear: 2032,
    age: 47,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 12175.95,
    interestRate: 0.03,
    interestAmount: 365.28,
    faceAmount: 350000.00,
    costOfInsurance: 342.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 12099.22
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 14,
    calendarYear: 2033,
    age: 48,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 12099.22,
    interestRate: 0.03,
    interestAmount: 362.98,
    faceAmount: 350000.00,
    costOfInsurance: 348.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 12014.20
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 15,
    calendarYear: 2034,
    age: 49,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 12014.20,
    interestRate: 0.03,
    interestAmount: 360.43,
    faceAmount: 350000.00,
    costOfInsurance: 354.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 11920.63
  },
   {
    policyId: 1,
    scenarioId: 5,
    policyYear: 16,
    calendarYear: 2035,
    age: 50,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 11920.63,
    interestRate: 0.03,
    interestAmount: 357.62,
    faceAmount: 350000.00,
    costOfInsurance: 360.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 11818.25
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 17,
    calendarYear: 2036,
    age: 51,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 11818.25,
    interestRate: 0.03,
    interestAmount: 354.55,
    faceAmount: 350000.00,
    costOfInsurance: 366.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 11706.79
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 18,
    calendarYear: 2037,
    age: 52,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 11706.79,
    interestRate: 0.03,
    interestAmount: 351.20,
    faceAmount: 350000.00,
    costOfInsurance: 372.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 11586.00
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 19,
    calendarYear: 2038,
    age: 53,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 11586.00,
    interestRate: 0.03,
    interestAmount: 347.58,
    faceAmount: 350000.00,
    costOfInsurance: 378.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 11455.58
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 20,
    calendarYear: 2039,
    age: 54,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 11455.58,
    interestRate: 0.03,
    interestAmount: 343.67,
    faceAmount: 350000.00,
    costOfInsurance: 384.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 11315.24
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 21,
    calendarYear: 2040,
    age: 55,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 11315.24,
    interestRate: 0.03,
    interestAmount: 339.46,
    faceAmount: 350000.00,
    costOfInsurance: 390.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 11164.70
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 22,
    calendarYear: 2041,
    age: 56,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 11164.70,
    interestRate: 0.03,
    interestAmount: 334.94,
    faceAmount: 350000.00,
    costOfInsurance: 396.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 11003.64
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 23,
    calendarYear: 2042,
    age: 57,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 11003.64,
    interestRate: 0.03,
    interestAmount: 330.11,
    faceAmount: 350000.00,
    costOfInsurance: 402.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 10831.75
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 24,
    calendarYear: 2043,
    age: 58,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 10831.75,
    interestRate: 0.03,
    interestAmount: 324.95,
    faceAmount: 350000.00,
    costOfInsurance: 408.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 10648.70
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 25,
    calendarYear: 2044,
    age: 59,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 10648.70,
    interestRate: 0.03,
    interestAmount: 319.46,
    faceAmount: 350000.00,
    costOfInsurance: 414.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 10454.17
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 26,
    calendarYear: 2045,
    age: 60,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 10454.17,
    interestRate: 0.03,
    interestAmount: 313.62,
    faceAmount: 350000.00,
    costOfInsurance: 420.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 10247.79
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 27,
    calendarYear: 2046,
    age: 61,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 10247.79,
    interestRate: 0.03,
    interestAmount: 307.43,
    faceAmount: 350000.00,
    costOfInsurance: 426.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 10029.22
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 28,
    calendarYear: 2047,
    age: 62,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 10029.22,
    interestRate: 0.03,
    interestAmount: 300.88,
    faceAmount: 350000.00,
    costOfInsurance: 432.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 9798.10
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 29,
    calendarYear: 2048,
    age: 63,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 9798.10,
    interestRate: 0.03,
    interestAmount: 293.94,
    faceAmount: 350000.00,
    costOfInsurance: 438.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 9554.04
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 30,
    calendarYear: 2049,
    age: 64,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 9554.04,
    interestRate: 0.03,
    interestAmount: 286.62,
    faceAmount: 350000.00,
    costOfInsurance: 444.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 9296.67
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 31,
    calendarYear: 2050,
    age: 65,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 9296.67,
    interestRate: 0.03,
    interestAmount: 278.90,
    faceAmount: 350000.00,
    costOfInsurance: 450.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 9025.57
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 32,
    calendarYear: 2051,
    age: 66,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 9025.57,
    interestRate: 0.03,
    interestAmount: 270.77,
    faceAmount: 350000.00,
    costOfInsurance: 456.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 8740.33
  },
  {
    policyId: 1,
    scenarioId: 5,
    policyYear: 33,
    calendarYear: 2052,
    age: 67,
    plannedPremium: 0.00,
    netValueBeginningOfYear: 8740.33,
    interestRate: 0.03,
    interestAmount: 262.21,
    faceAmount: 350000.00,
    costOfInsurance: 462.00,
    withdrawal: 0.00,
    policyLoan: 0.00,
    loanInterestRate: 0.05,
    loanInterest: 0.00,
    loanRepayment: 0.00,
    loanOutstanding: 0.00,
    charges: 100.00,
    netCashValue: 8440.54
  }
  ];
};
